'use client';

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { Button } from '@/components/ui/button';
import { 
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import {
  Cloud,
  Menu,
  X,
  Zap,
  Shield,
  Cpu,
  Bot,
  Users,
  Building,
  GraduationCap
} from 'lucide-react';
import { cn } from '@/lib/utils';

export function LandingNavigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    {
      title: 'Product',
      items: [
        {
          title: 'Features',
          description: 'Explore Omnispace capabilities',
          href: '#features',
          icon: Zap,
        },
        {
          title: 'Architecture',
          description: 'Technical overview and stack',
          href: '#architecture',
          icon: Cpu,
        },
        {
          title: 'Security',
          description: 'Enterprise-grade security',
          href: '#security',
          icon: Shield,
        },
        {
          title: 'AI Integration',
          description: 'AI-powered workspace assistance',
          href: '#ai',
          icon: Bot,
        },
      ],
    },
    {
      title: 'Solutions',
      items: [
        {
          title: 'Development Teams',
          description: 'Scalable dev environments',
          href: '#use-cases',
          icon: Users,
        },
        {
          title: 'Enterprise',
          description: 'Enterprise-grade deployments',
          href: '#enterprise',
          icon: Building,
        },
        {
          title: 'Education',
          description: 'Learning and training environments',
          href: '#education',
          icon: GraduationCap,
        },
      ],
    },
  ];

  return (
    <header 
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
        isScrolled 
          ? 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-800' 
          : 'bg-transparent'
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
              <Cloud className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              Omnispace
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            <NavigationMenu>
              <NavigationMenuList>
                {navigationItems.map((item) => (
                  <NavigationMenuItem key={item.title}>
                    <NavigationMenuTrigger className="text-gray-700 dark:text-gray-300">
                      {item.title}
                    </NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <div className="grid gap-3 p-6 w-[400px]">
                        {item.items.map((subItem) => (
                          <NavigationMenuLink key={subItem.title} asChild>
                            <Link
                              href={subItem.href}
                              className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                            >
                              <div className="flex items-center gap-2">
                                {subItem.icon && <subItem.icon className="h-4 w-4" />}
                                <div className="text-sm font-medium leading-none">
                                  {subItem.title}
                                </div>
                              </div>
                              <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                                {subItem.description}
                              </p>
                            </Link>
                          </NavigationMenuLink>
                        ))}
                      </div>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                ))}
                
                <NavigationMenuItem>
                  <Link href="#pricing" className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                    Pricing
                  </Link>
                </NavigationMenuItem>
                
                <NavigationMenuItem>
                  <Link href="#demo" className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                    Demo
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center gap-4">
            <Link href="/vm-manager">
              <Button variant="ghost" className="text-gray-700 dark:text-gray-300">
                VM Manager
              </Button>
            </Link>
            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                Get Started
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </Button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
            <div className="px-4 py-6 space-y-4">
              {navigationItems.map((item) => (
                <div key={item.title} className="space-y-2">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {item.title}
                  </div>
                  <div className="pl-4 space-y-2">
                    {item.items.map((subItem) => (
                      <Link
                        key={subItem.title}
                        href={subItem.href}
                        className="block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {subItem.title}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
              
              <div className="pt-4 space-y-2">
                <Link href="#pricing" className="block text-gray-700 dark:text-gray-300">
                  Pricing
                </Link>
                <Link href="#demo" className="block text-gray-700 dark:text-gray-300">
                  Demo
                </Link>
              </div>
              
              <div className="pt-4 space-y-2">
                <Link href="/vm-manager" className="block">
                  <Button variant="outline" className="w-full">
                    VM Manager
                  </Button>
                </Link>
                <Link href="/dashboard" className="block">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
